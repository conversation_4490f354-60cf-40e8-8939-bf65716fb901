import { Text } from '@/components';
import type { BottomSheetBackdropProps } from '@gorhom/bottom-sheet';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import { forwardRef, useCallback } from 'react';

const snapPoints = ['80%'];

type Props = {};

/**
 * @description 创建记录底部弹窗
 * <AUTHOR>
 * @date 2025-08-28
 */
export const AddRecordBottomSheet = forwardRef<BottomSheetModal, Props>(
  (props, ref) => {
    // const {} = props;

    const renderBackdrop = useCallback(
      (p: BottomSheetBackdropProps) => (
        <BottomSheetBackdrop {...p} appearsOnIndex={0} disappearsOnIndex={-1} />
      ),
      [],
    );

    return (
      <BottomSheetModal
        ref={ref}
        name="AddRecordBottomSheet"
        snapPoints={snapPoints}
        enablePanDownToClose
        enableDynamicSizing={false}
        enableContentPanningGesture={false}
        backdropComponent={renderBackdrop}
      >
        <bottomsh className="flex-1 bg-blue-500">
          <Text>Awesome 🎉</Text>
        </bottoms>
      </BottomSheetModal>
    );
  },
);
