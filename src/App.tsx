import { RootTabsNavigation } from '@/navigation/RootTabsNavigation';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PortalHost } from '@rn-primitives/portal';
import { StatusBar, useColorScheme } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import './styles/global.css';

export const App = () => {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <GestureHandlerRootView className="flex-1">
      <SafeAreaProvider>
        <BottomSheetModalProvider>
          <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />

          <RootTabsNavigation />
        </BottomSheetModalProvider>
      </SafeAreaProvider>

      <PortalHost />
    </GestureHandlerRootView>
  );
};
